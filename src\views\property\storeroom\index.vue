<template>
  <div class="app-container oa-pm" element-loading-spinner="el-icon-loading" element-loading-text="更新中">
    <!--工具栏-->
    <div class="head-container">
      <search-header :category-list="categoryList" :dict="dict" :permission="permission" />
      <crudOperation :permission="permission">
        <!--一系列更新按钮-->
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @importProject="importProject"
        />
      </crudOperation>
    </div>
    <!--表格渲染-->
    <div ref="projectBody" class="project-table-content">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        lazy
        row-key="id"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          v-for="(header, initIndex) in tableHeaders"
          :key="header.label + initIndex"
          v-bind="getColumnProps(header)"
        >
          <template slot-scope="scope">
            <see-cell :current-cell="scope" :header="header" :permission="permission" />
          </template>
        </el-table-column>
        <transition name="fade">
          <el-table-column fixed="right" label="操作" width="240">
            <template slot-scope="scope">
              <udOperation :data="scope.row" :msg="`确定删除这个仓库吗？此操作不能撤销！`" :permission="permission" />
            </template>
          </el-table-column>
        </transition>
      </el-table>

      <!--分页组件-->
      <pagination />
    </div>
    <!--导入项目-->
    <upload-file ref="refUploadFile" @getlist="crud.toQuery()" />
  </div>
</template>

<script>
import amDepots from '@/api/property/amDepot'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination';
import UploadFile from './components/uploadFile.vue'
import SeeCell from './components/seeCell.vue';
import CustomActionButton from './components/customActionButton.vue';
import SearchHeader from './components/searchHeader.vue'
import { mapGetters } from 'vuex'
import {
  formatterTableData,
  formatterTableHeader
} from './utils/commonFun'
import {
  permission
} from './utils/field'
// crud交由presenter持有
const defaultForm = {
  id: null
}
export default {
  name: 'Storeroom',
  components: {
    UploadFile,
    SearchHeader,
    crudOperation,
    udOperation,
    pagination,
    CustomActionButton,
    SeeCell
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '仓库',
      url: 'api/amDepot/small',
      sort: [],
      query: {
        enabled: 1,
        title: '',
        fv4: '',
        sort: ['createTime,desc']
      },
      crudMethod: { ...amDepots },
      optShow: {
        add: true,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: [],
  data() {
    return {
      tableData: [],
      permission,
      toolbarID: 'toolbarID',
      bindId: '',
      tableHeaders: [],
      categoryList: [],
      selectOption: {
        fv2: []
      },
      filterValues: {},
      filteredData: []
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    'crud.data': {
      async handler(val, oldVal) {
        // 处理表头和筛选选项
        this.tableHeaders = formatterTableHeader(val)
        this.tableData = formatterTableData(val)
      },
      deep: true
    }
  },
  async created() {
    this.crud.operToggle = true
    const { bindId } = this.$config.storeroom_key
    this.bindId = bindId;
    this.crud.query.bindId = bindId
    this.crud.toQuery();
  },
  methods: {
    // 点击添加-库房
    [CRUD.HOOK.beforeToAdd]() {
      this.$router.push({ name: 'AddStoreroomForm', query: { bindId: this.bindId, command: 1, fv1: '项目' }})
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      const { id, bindId, fv1 } = form
      this.$router.push({ name: 'EditStoreroomForm', query: { id, bindId, fv1 }})
    },
    // 表格属性
    getColumnProps(header) {
      return {
        'header-align': header.align ? header.align : 'left',
        align: header.align,
        fixed: header.fixed,
        label: header.label,
        prop: header.prop,
        'show-overflow-tooltip': true,
        sortable: header.sortable || false,
        width: header.width
      };
    },
    // 导入项目
    importProject() {
      const { bindId, categoryId } = this.$config.storeroom_key
      this.$refs.refUploadFile.init({ bindId, categoryId });
    },
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.bindId
    },
    [CRUD.HOOK.beforeResetQuery]() {
      this.$refs.table.clearSort()
    },
    handleMany(data) {
      const { item } = data
      typeof this[item.fun] === 'function' && this[item.fun](data)
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
//操作按钮相关
.operate-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

}

.project-table-content {
  position: relative;

  .hamburger-container {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 8;
    line-height: 50px;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;
    background: rgba(0, 0, 0, .09);

    &:hover {
      background: rgba(0, 0, 0, .19)
    }
  }
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .vue-treeselect__control,
::v-deep .vue-treeselect__placeholder,
::v-deep .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}

.info-vxe-toolbar {
  padding: 0 !important;
  width: 28px !important;
  height: 28px !important;

  .vxe-button.vxe-tools--operate.type--button {
    min-width: 28px !important;
    height: 28px !important;
    padding: 0px 12px !important;

  }

  .vxe-tools--operate.is--circle {
    border-radius: 0 !important;
  }

}

.custom-filter {
  padding: 8px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.filter-select-dropdown {
  z-index: 999999 !important;
}

.my-filter {
  padding: 12px;

  .filter-footer {
    margin-top: 12px;
    text-align: right;

    >button {
      margin-left: 8px;
    }
  }
}

.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;

  .el-icon-arrow-down {
    cursor: pointer;
    padding: 0 4px;
    font-size: 16px;
    color: #909399;
    transition: transform .3s;

    &:hover {
      color: #409EFF;
    }

    &.is-active {
      color: #409EFF;
      transform: rotate(180deg);
    }
  }
}

.filter-content {
  padding: 12px;

  .filter-footer {
    margin-top: 12px;
    text-align: right;

    >button {
      margin-left: 8px;
    }
  }
}
</style>

<style lang="scss">
.filter-popover {
  min-width: 240px !important;
}
</style>
