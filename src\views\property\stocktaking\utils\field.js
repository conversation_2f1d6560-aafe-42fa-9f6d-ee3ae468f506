// 项目列表所有权限
export const permission = {
  add: ['admin', 'amStockCount:add'],
  edit: ['admin', 'amStockCount:edit'],
  del: ['admin', 'amStockCount:del'],
  updateT: ['admin', 'amStockCount:updateFormStruct'],
  updateR: ['admin', 'amStockCount:updateRelation'],
  importXlsWithRule: ['admin', 'amStockCount:importXlsWithRule']
  // exportProject: ['admin', 'amStockCount:exportProject'],
  // previewProject: ['admin', 'amStockCount:previewProject'],
}
// 项目列表表头
export const tableHeader = [
  { label: '资产编号', prop: 'basicNo', fixed: 'left', headerAlign: 'left', align: 'left', width: 200 },
  // { label: '订单编号', prop: 'orderNo', headerAlign: 'left', align: 'left', width: 200 },
  { label: '库房', prop: 'depot', headerAlign: 'left', align: 'left', width: 200 },
  { label: '项目', prop: 'pm', align: 'left', headerAlign: 'left', width: 300 },
  { label: '设备名称', prop: 'device', align: 'left', headerAlign: 'left', width: 200 },
  { label: '品牌', prop: 'brand', align: 'left', headerAlign: 'left', width: 150 },
  { label: '型号', prop: 'model', align: 'left', headerAlign: 'left', width: 150 },
  { label: '入库数量', prop: 'stockInAmount', align: 'left', headerAlign: 'left', width: 90 },
  { label: '库存数量', prop: 'stockAmount', align: 'left', headerAlign: 'left', width: 90 },
  { label: '出库数量', prop: 'stockOutAmount', align: 'left', headerAlign: 'left', width: 90 },
  { label: '盘库数量', prop: 'stockCount', align: 'left', headerAlign: 'left', width: 90 },
  { label: '盘增/减', prop: 'stockDifference', align: 'left', headerAlign: 'left', width: 90 },
  { label: '盘库时间', prop: 'updateTime', align: 'left', headerAlign: 'left', width: 150 },
  { label: '盘库人', prop: 'updateBy', align: 'left', headerAlign: 'left', width: 100 }
  // { label: '备注', prop: 'ft1', align: 'left', headerAlign: 'left' }
]

// 定义按钮组
export const updateButtonsLists = [
  {
    id: '1',
    label: '导入',
    permission: permission.importXlsWithRule,
    fun: 'importProject',
    size: 'mini',
    className: [],
    icon: 'plus',
    type: 'primary'
  }
  // {
  //   id: '6',
  //   label: '导出',
  //   permission: permission.exportProject,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'download',
  //   type: 'primary',
  //   query: {
  //     fileType: 'xls'
  //   }
  // },
  // {
  //   id: '7',
  //   label: '预览',
  //   permission: permission.previewProject,
  //   fun: 'exportProject',
  //   size: 'mini',
  //   className: [],
  //   icon: 'view',
  //   type: 'primary',
  //   query: {
  //     fileType: 'html'
  //   }
  // }
]

