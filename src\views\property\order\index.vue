<template>
  <div class="app-container oa-pm" element-loading-spinner="el-icon-loading" element-loading-text="更新中">
    <!--工具栏-->
    <div class="head-container">
      <search-header :category-list="categoryList" :dict="dict" :permission="permission" />
      <crudOperation :permission="permission">
        <!--一系列更新按钮-->
        <custom-action-button
          slot="right"
          :bind-id="bindId"
          :current-crud="crud"
          :permission="permission"
          @importProject="importProject"
          @batchImport="batchImport"
        />
      </crudOperation>
    </div>
    <!--表格渲染-->
    <div ref="projectBody" class="project-table-content">
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="tableData"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        lazy
        row-key="id"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          v-for="(header, initIndex) in tableHeaders"
          :key="header.label + initIndex"
          v-bind="getColumnProps(header)"
        >
          <template slot-scope="scope">
            <see-cell :current-cell="scope" :header="header" :permission="permission" />
          </template>
        </el-table-column>
        <transition name="fade">
          <el-table-column fixed="right" label="操作" width="240">
            <template slot-scope="scope">
              <udOperation :data="scope.row" :msg="`确定删除这个仓库吗？此操作不能撤销！`" :permission="permission" />
              <el-button
                v-if="scope.row.status !== '已入库'"
                v-permission="permission.stockIn"
                size="mini"
                type="success"
                @click="addStore(scope.row)"
              >入库</el-button>
              <min-crud-operation
                v-else
                :handle-many="handleMany"
                :many-option="manyOption"
                :scope="scope"
                title="详情"
              />
            </template>
          </el-table-column>
        </transition>
      </el-table>

      <!--分页组件-->
      <pagination />
    </div>
    <!--导入项目-->
    <upload-file ref="refUploadFile" @getlist="crud.toQuery()" />
    <!-- 订单表单 -->
    <order-form v-if="orderFormShow" ref="refOrderForm" @successAction="successAction" />
    <!-- 入库表单 -->
    <store-form v-if="storeFormShow" ref="refStoreForm" @successAction="successAction" />
    <!-- 批量入库对话框 -->
    <batch-import-dialog ref="batchImportDialog" @success="handleBatchImportSuccess" />
  </div>
</template>

<script>
import amOrder from '@/api/property/amOrder'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import minCrudOperation from '@crud/UD.operation.mini'
import pagination from '@crud/Pagination';
import UploadFile from './components/uploadFile.vue'
import SeeCell from './components/seeCell.vue';
import CustomActionButton from './components/customActionButton.vue';
import SearchHeader from './components/searchHeader.vue'
import OrderForm from './components/orderform.vue';
import StoreForm from '@/views/property/store/components/storeform.vue'
import BatchImportDialog from './components/batchImportDialog.vue'
import { mapGetters } from 'vuex'
import {
  formatterTableData,
  formatterTableHeader
} from './utils/commonFun'
import {
  permission,
  manyOption
} from './utils/field'
// crud交由presenter持有
const defaultForm = {
  id: null
}
export default {
  name: 'Order',
  components: {
    OrderForm,
    StoreForm,
    UploadFile,
    SearchHeader,
    crudOperation,
    udOperation,
    pagination,
    CustomActionButton,
    SeeCell,
    minCrudOperation,
    BatchImportDialog
  },
  cruds() {
    return CRUD({
      queryOnPresenterCreated: false,
      title: '订单',
      url: 'api/amOrder/small',
      sort: [],
      query: {
        basicNo: '',
        orderNo: '',
        enabled: 1,
        title: '',
        fv4: '',
        sort: ['createTime,desc']
      },
      crudMethod: { ...amOrder },
      optShow: {
        add: false,
        edit: false,
        del: false,
        download: false,
        reset: true,
        rightGroup: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: [],
  data() {
    return {
      manyOption,
      orderFormShow: false,
      storeFormShow: false,
      tableData: [],
      permission,
      toolbarID: 'toolbarID',
      bindId: '',
      tableHeaders: [],
      categoryList: [],
      selectOption: {
        fv2: []
      },
      filterValues: {},
      filteredData: []
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
    'crud.data': {
      async handler(val, oldVal) {
        // 处理表头和筛选选项
        this.tableHeaders = formatterTableHeader(val)
        this.tableData = formatterTableData(val)
      },
      deep: true
    }
  },
  async created() {
    this.crud.operToggle = true
    const { bindId } = this.$config.order_key
    this.bindId = bindId;
    this.crud.query.bindId = bindId
    const { basicNo, orderNo } = this.$route.query;
    if (basicNo) {
      this.crud.query.basicNo = basicNo
    }
    if (orderNo) {
      this.crud.query.orderNo = orderNo
    }
    this.crud.toQuery();
  },
  methods: {
    // 详情
    toDetail(data) {
      const { row, item } = data
      this.$router.push({
        name: item.routerName,
        query: {
          basicNo: row.basicData.basicNo
        }
      })
    },
    // 点击入库
    async addStore(row) {
      this.storeFormShow = true
      await this.$nextTick();
      const data = {
        type: 1,
        row
      }
      this.$refs.refStoreForm.init(data)
    },
    // 编辑成功
    successAction() {
      this.crud.refresh()
      this.orderFormShow = false
    },
    async [CRUD.HOOK.beforeToEdit](crud, form) {
      const { id } = form
      this.orderFormShow = true
      await this.$nextTick()
      const data = {
        type: 2,
        id
      }
      this.$refs.refOrderForm.init(data)
    },
    // 表格属性
    getColumnProps(header) {
      return {
        'header-align': header.align ? header.align : 'left',
        align: header.align,
        fixed: header.fixed,
        label: header.label,
        prop: header.prop,
        'show-overflow-tooltip': true,
        sortable: header.sortable || false,
        width: header.width
      };
    },
    // 导入项目
    importProject() {
      const { bindId, categoryId } = this.$config.order_key
      this.$refs.refUploadFile.init({ bindId, categoryId });
    },
    // 批量入库
    batchImport() {
      this.$refs.batchImportDialog.show()
    },
    // 批量入库成功回调
    handleBatchImportSuccess() {
      this.crud.refresh()
    },
    [CRUD.HOOK.beforeRefresh]() {
      this.crud.query.bindId = this.bindId
    },
    [CRUD.HOOK.beforeResetQuery]() {
      this.$refs.table.clearSort()
    },
    handleMany(data) {
      const { item } = data
      typeof this[item.fun] === 'function' && this[item.fun](data)
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
//操作按钮相关
.operate-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

}

.project-table-content {
  position: relative;

  .hamburger-container {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 8;
    line-height: 50px;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;
    background: rgba(0, 0, 0, .09);

    &:hover {
      background: rgba(0, 0, 0, .19)
    }
  }
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .vue-treeselect__control,
::v-deep .vue-treeselect__placeholder,
::v-deep .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}

.info-vxe-toolbar {
  padding: 0 !important;
  width: 28px !important;
  height: 28px !important;

  .vxe-button.vxe-tools--operate.type--button {
    min-width: 28px !important;
    height: 28px !important;
    padding: 0px 12px !important;

  }

  .vxe-tools--operate.is--circle {
    border-radius: 0 !important;
  }

}

.custom-filter {
  padding: 8px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.filter-select-dropdown {
  z-index: 999999 !important;
}

.my-filter {
  padding: 12px;

  .filter-footer {
    margin-top: 12px;
    text-align: right;

    >button {
      margin-left: 8px;
    }
  }
}

.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;

  .el-icon-arrow-down {
    cursor: pointer;
    padding: 0 4px;
    font-size: 16px;
    color: #909399;
    transition: transform .3s;

    &:hover {
      color: #409EFF;
    }

    &.is-active {
      color: #409EFF;
      transform: rotate(180deg);
    }
  }
}

.filter-content {
  padding: 12px;

  .filter-footer {
    margin-top: 12px;
    text-align: right;

    >button {
      margin-left: 8px;
    }
  }
}
</style>

<style lang="scss">
.filter-popover {
  min-width: 240px !important;
}
</style>
